'use client'

import React, { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { NotificationContainer } from './AnimatedNotification'
import { Bell, Search, User } from 'lucide-react'
import { useHydrationSafe } from '@/hooks/useHydrationSafe'

// Import the new YoutubeSidebar component
import YoutubeSidebar from './YoutubeSidebar'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
}

// Page titles mapping
const pageInfo: Record<string, { title: string; subtitle: string }> = {
  '/dashboard': { title: 'Dashboard', subtitle: 'Overview & analytics' },
  '/products': { title: 'Products', subtitle: 'Manage your store inventory' },
  '/customers': { title: 'Customers', subtitle: 'Customer database' },
  '/debts': { title: 'Debts', subtitle: 'Track customer debts' },
  '/payments': { title: 'Payments', subtitle: 'Payment records' },
  '/reports': { title: 'Reports', subtitle: 'Business insights' },
}

export default function DashboardLayout({
  children,
  title,
  subtitle: _subtitle,
  actions: _actions
}: DashboardLayoutProps) {
  // Suppress unused variable warnings - parameters kept for interface consistency
  void _subtitle
  void _actions

  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const isHydrated = useHydrationSafe()

  // Get current page info
  const currentPageInfo = pageInfo[pathname] || { title: 'Dashboard', subtitle: 'Overview & analytics' }
  const pageTitle = title || currentPageInfo.title

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-dropdown]')) {
        setShowNotifications(false)
        setShowProfile(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Show loading state during hydration
  if (!isHydrated) {
    return (
      <div className="dashboard-layout min-h-screen bg-gray-50">
        {/* Loading Sidebar */}
        <aside className="fixed top-0 left-0 z-50 h-full w-64 bg-white border-r border-gray-200 -translate-x-full lg:translate-x-0">
          <div className="flex items-center h-14 px-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg"></div>
              <div>
                <div className="h-4 bg-gray-300 rounded w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-12"></div>
              </div>
            </div>
          </div>
          <div className="p-4 space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-3 p-2">
                <div className="w-5 h-5 bg-gray-300 rounded"></div>
                <div className="h-4 bg-gray-300 rounded flex-1"></div>
              </div>
            ))}
          </div>
        </aside>

        {/* Loading Header */}
        <header className="fixed top-0 right-0 left-64 h-14 bg-white border-b border-gray-200 z-30">
          <div className="flex items-center justify-between h-full px-4">
            <div className="h-6 bg-gray-300 rounded w-32"></div>
            <div className="flex space-x-2">
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </header>

        {/* Loading Content */}
        <main className="ml-64 pt-14">
          <div className="p-6">
            <div className="h-8 bg-gray-300 rounded w-48 mb-4"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="dashboard-layout min-h-screen bg-gray-50">
      {/* YouTube-Style Sidebar */}
      <YoutubeSidebar
        isCollapsed={isCollapsed}
        onToggleCollapse={() => setIsCollapsed(!isCollapsed)}
        isMobileMenuOpen={isMobileMenuOpen}
        onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      />

      {/* YouTube-Style Header */}
      <header className={`fixed top-0 right-0 h-14 bg-white border-b border-gray-200 z-30 transition-all duration-300 ${
        isCollapsed ? 'left-16' : 'left-64'
      }`}>
        <div className="flex items-center justify-between h-full px-4">
          {/* Left Section */}
          <div className="flex items-center space-x-4">
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 rounded-full hover:bg-gray-100"
            >
              <svg className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {/* Desktop Hamburger Menu */}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="hidden lg:flex p-2 rounded-full hover:bg-gray-100"
            >
              <svg className="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {/* Page Title */}
            <div className="hidden sm:block">
              <h1 className="text-lg font-medium text-gray-900">
                {pageTitle}
              </h1>
            </div>
          </div>

          {/* Center Section - Search */}
          <div className="flex-1 max-w-2xl mx-8 hidden md:block">
            <div className="relative">
              <div className="flex items-center bg-gray-50 border border-gray-300 rounded-full overflow-hidden hover:border-gray-400 focus-within:border-blue-500 transition-colors">
                <input
                  type="text"
                  placeholder="Search products, customers, debts..."
                  className="flex-1 px-4 py-2 bg-transparent text-sm text-gray-900 placeholder-gray-500 focus:outline-none"
                />
                <button className="px-6 py-2 bg-gray-100 border-l border-gray-300 hover:bg-gray-200 transition-colors">
                  <Search className="h-4 w-4 text-gray-600" />
                </button>
              </div>
            </div>
          </div>

          {/* Right Section */}
          <div className="flex items-center space-x-2">
            {/* Mobile Search Button */}
            <button className="md:hidden p-2 rounded-full hover:bg-gray-100">
              <Search className="h-5 w-5 text-gray-700" />
            </button>

            {/* Notifications */}
            <button 
              className="relative p-2 rounded-full hover:bg-gray-100"
              onClick={() => setShowNotifications(!showNotifications)}
            >
              <Bell className="h-5 w-5 text-gray-700" />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            {/* Profile */}
            <button 
              className="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100"
              onClick={() => setShowProfile(!showProfile)}
            >
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
            </button>
          </div>
        </div>

        {/* Notifications Dropdown */}
        {showNotifications && (
          <div className="absolute right-4 top-12 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <div className="p-4 border-b border-gray-100">
              <h3 className="font-medium text-gray-900">Notifications</h3>
            </div>
            <div className="p-4 space-y-3 max-h-64 overflow-y-auto">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Low Stock Alert</p>
                  <p className="text-xs text-gray-600">3 products are running low</p>
                  <p className="text-xs text-gray-500 mt-1">2 minutes ago</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Profile Dropdown */}
        {showProfile && (
          <div className="absolute right-4 top-12 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <div className="p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Store Owner</p>
                  <p className="text-sm text-gray-600">Administrator</p>
                </div>
              </div>
              <div className="space-y-2">
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                  Settings
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                  Sign out
                </button>
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Main Content Area */}
      <main className={`transition-all duration-300 pt-14 ${
        isCollapsed ? 'ml-16' : 'ml-64'
      }`}>
        <div className="p-6">
          {children}
        </div>
      </main>

      {/* Notification Container */}
      <NotificationContainer />
    </div>
  )
}
