/** @type {import('next').NextConfig} */
const nextConfig = {
  // Complete SSR disable for hydration issues
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // React strict mode disabled
  reactStrictMode: false,

  // Power by header
  poweredByHeader: false,

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Optimized webpack configuration for YouTube sidebar
  webpack: (config, { dev, isServer }) => {
    // Development optimizations
    if (dev) {
      // Optimize watch options for better performance
      config.watchOptions = {
        ...config.watchOptions,
        ignored: /node_modules/,
        poll: 1000,
        aggregateTimeout: 300,
      }

      // Optimize module resolution for faster builds
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
      }
    }

    // Bundle analyzer (only when requested)
    if (process.env.ANALYZE === 'true' && !isServer) {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: '../bundle-analyzer-report.html'
        })
      )
    }

    return config
  },
}

module.exports = nextConfig
