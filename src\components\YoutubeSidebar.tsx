'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { LucideIcon } from 'lucide-react'
import {
  Package,
  Users,
  CreditCard,
  Receipt,
  BarChart3,
  Home,
  Store,
  Settings,
  X,
  Plus,
  FileText,
  TrendingUp,
  Clock,
  Star,
  Menu,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { useNavigationData } from '@/hooks/useNavigationData'
import { useState } from 'react'
import { useHydrationSafe } from '@/hooks/useHydrationSafe'

interface NavigationItem {
  name: string
  href: string
  icon: LucideIcon
  badge?: string | number
  isNew?: boolean
}

interface NavigationSection {
  title?: string
  items: NavigationItem[]
  collapsible?: boolean
  defaultExpanded?: boolean
}

interface YoutubeSidebarProps {
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  isMobileMenuOpen?: boolean
  onMobileMenuToggle?: () => void
}

export default function YoutubeSidebar({
  isCollapsed = false,
  onToggleCollapse,
  isMobileMenuOpen = false,
  onMobileMenuToggle
}: YoutubeSidebarProps) {
  const pathname = usePathname()
  const navigationData = useNavigationData()
  const isHydrated = useHydrationSafe()
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    main: true,
    business: true,
    reports: true,
    quickActions: false,
    recent: false
  })

  // Don't render until hydrated to prevent SSR/client mismatch
  if (!isHydrated) {
    return (
      <aside className="fixed top-0 left-0 z-50 h-full w-64 bg-white border-r border-gray-200 -translate-x-full lg:translate-x-0">
        <div className="flex items-center h-14 px-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-300 rounded-lg animate-pulse"></div>
            <div>
              <div className="h-4 bg-gray-300 rounded w-24 mb-1 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
        </div>
        <div className="p-4 space-y-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex items-center space-x-3 p-2">
              <div className="w-5 h-5 bg-gray-300 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-300 rounded flex-1 animate-pulse"></div>
            </div>
          ))}
        </div>
      </aside>
    )
  }

  // Define navigation sections with dynamic data
  const mainNavigation: NavigationItem[] = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  ]

  const businessNavigation: NavigationItem[] = [
    {
      name: 'Products',
      href: '/products',
      icon: Package,
      badge: navigationData.loading ? '...' : navigationData.productsCount
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: Users,
      badge: navigationData.loading ? '...' : navigationData.customersCount
    },
    {
      name: 'Debts',
      href: '/debts',
      icon: CreditCard,
      badge: navigationData.loading ? '...' : navigationData.debtsCount
    },
    {
      name: 'Payments',
      href: '/payments',
      icon: Receipt,
      badge: navigationData.loading ? '...' : navigationData.paymentsCount
    },
  ]

  const reportsNavigation: NavigationItem[] = [
    { name: 'Business Reports', href: '/reports', icon: FileText },
    { name: 'Analytics Dashboard', href: '/analytics', icon: TrendingUp },
  ]

  const quickActions: NavigationItem[] = [
    { name: 'Add Product', href: '/products/new', icon: Plus, isNew: true },
    { name: 'Add Customer', href: '/customers/new', icon: Users },
    { name: 'Record Payment', href: '/payments/new', icon: Receipt },
    { name: 'Record Debt', href: '/debts/new', icon: CreditCard },
  ]

  const recentItems: NavigationItem[] = [
    { name: 'Recent Products', href: '/products', icon: Clock },
    { name: 'Recent Customers', href: '/customers', icon: Clock },
    { name: 'Recent Payments', href: '/payments', icon: Clock },
  ]

  const navigationSections: NavigationSection[] = [
    { items: mainNavigation },
    { title: 'Business', items: businessNavigation, collapsible: true, defaultExpanded: true },
    { title: 'Reports', items: reportsNavigation, collapsible: true, defaultExpanded: true },
    { title: 'Quick Actions', items: quickActions, collapsible: true, defaultExpanded: false },
    { title: 'Recent', items: recentItems, collapsible: true, defaultExpanded: false },
  ]

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }))
  }

  // Render navigation item
  const renderNavigationItem = (item: NavigationItem, sectionKey?: string) => {
    const isActive = pathname === item.href
    const showBadge = item.badge && !isCollapsed
    const showNewIndicator = item.isNew && !isCollapsed

    return (
      <Link
        key={item.href}
        href={item.href}
        onClick={() => isMobileMenuOpen && onMobileMenuToggle?.()}
        className={`
          group relative flex items-center rounded-lg transition-all duration-200 youtube-nav-item
          ${isCollapsed ? 'p-3 justify-center mx-2' : 'px-3 py-2.5 mx-3'}
          ${isActive
            ? 'bg-gray-100 text-gray-900 youtube-nav-active'
            : 'text-gray-700 hover:bg-gray-50'
          }
        `}
        title={isCollapsed ? item.name : undefined}
        aria-label={isCollapsed ? item.name : undefined}
      >
        <item.icon className={`
          ${isCollapsed ? 'w-5 h-5' : 'w-5 h-5 mr-3'}
          ${isActive ? 'text-green-600' : 'text-gray-600 group-hover:text-gray-700'}
          transition-colors duration-200
        `} />

        {!isCollapsed && (
          <>
            <span className="text-sm font-medium flex-1 truncate">{item.name}</span>

            {/* Badge */}
            {showBadge && (
              <span className={`
                text-xs px-2 py-0.5 rounded-full ml-2 transition-colors duration-200
                ${navigationData.loading
                  ? 'bg-gray-100 text-gray-400 animate-pulse'
                  : 'bg-gray-200 text-gray-700 group-hover:bg-gray-300'
                }
              `}>
                {item.badge}
              </span>
            )}

            {/* New Indicator */}
            {showNewIndicator && (
              <span className="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            )}
          </>
        )}

        {/* Tooltip for collapsed state */}
        {isCollapsed && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            {item.name}
            {item.badge && (
              <span className="ml-1 px-1.5 py-0.5 bg-gray-700 rounded text-xs">
                {item.badge}
              </span>
            )}
          </div>
        )}
      </Link>
    )
  }

  // Render section header
  const renderSectionHeader = (section: NavigationSection, sectionKey: string) => {
    if (!section.title || isCollapsed) return null

    const isExpanded = expandedSections[sectionKey]
    const ChevronIcon = isExpanded ? ChevronDown : ChevronRight

    return (
      <div className="px-3 mb-2">
        {section.collapsible ? (
          <button
            onClick={() => toggleSection(sectionKey)}
            className="flex items-center w-full text-xs font-semibold text-gray-500 uppercase tracking-wider hover:text-gray-700 transition-colors duration-200 py-2"
            aria-expanded={isExpanded}
            aria-controls={`section-${sectionKey}`}
          >
            <ChevronIcon className="w-3 h-3 mr-1" />
            {section.title}
          </button>
        ) : (
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider py-2">
            {section.title}
          </h3>
        )}
      </div>
    )
  }

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 backdrop-blur-sm"
          onClick={() => onMobileMenuToggle?.()}
          aria-hidden="true"
        />
      )}

      {/* YouTube-Style Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 z-50 h-full bg-white border-r border-gray-200 transition-all duration-300 youtube-sidebar
          ${isCollapsed ? 'w-16' : 'w-64'}
          ${isMobileMenuOpen ? 'translate-x-0 shadow-2xl' : '-translate-x-full lg:translate-x-0'}
        `}
        aria-label="Main navigation"
      >
        {/* Header */}
        <div className={`
          flex items-center h-14 px-4 border-b border-gray-200 bg-white sticky top-0 z-10
          ${isCollapsed ? 'justify-center' : 'justify-between'}
        `}>
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-green-700 rounded-lg flex items-center justify-center shadow-sm">
                <Store className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-base font-semibold text-gray-900">Caparan Tindahan</h1>
                <p className="text-xs text-gray-500">Admin Dashboard</p>
              </div>
            </div>
          )}

          {!isCollapsed && (
            <button
              onClick={() => onToggleCollapse?.()}
              className="hidden lg:flex p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              title="Collapse sidebar"
              aria-label="Collapse sidebar"
            >
              <Menu className="h-4 w-4 text-gray-600" />
            </button>
          )}

          {isCollapsed && (
            <button
              onClick={() => onToggleCollapse?.()}
              className="w-8 h-8 bg-gradient-to-br from-green-600 to-green-700 rounded-lg flex items-center justify-center shadow-sm hover:shadow-md transition-shadow duration-200"
              title="Expand sidebar"
              aria-label="Expand sidebar"
            >
              <Store className="h-4 w-4 text-white" />
            </button>
          )}

          {/* Mobile Close Button */}
          {isMobileMenuOpen && (
            <button
              onClick={() => onMobileMenuToggle?.()}
              className="lg:hidden p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              aria-label="Close navigation menu"
            >
              <X className="h-4 w-4 text-gray-600" />
            </button>
          )}
        </div>

        {/* Navigation Content */}
        <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <nav className="py-4" role="navigation" aria-label="Sidebar navigation">
            {navigationSections.map((section, sectionIndex) => {
              const sectionKey = section.title?.toLowerCase().replace(/\s+/g, '') || `section-${sectionIndex}`
              const isExpanded = section.collapsible ? expandedSections[sectionKey] : true

              return (
                <div key={sectionKey} className="mb-6 last:mb-4">
                  {renderSectionHeader(section, sectionKey)}

                  <div
                    id={`section-${sectionKey}`}
                    className={`
                      space-y-1 transition-all duration-300 overflow-hidden
                      ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
                    `}
                  >
                    {section.items.map((item) => renderNavigationItem(item, sectionKey))}
                  </div>

                  {/* Section Divider */}
                  {!isCollapsed && sectionIndex < navigationSections.length - 1 && isExpanded && (
                    <div className="mx-3 mt-4 border-b border-gray-100"></div>
                  )}
                </div>
              )
            })}
          </nav>

          {/* Footer */}
          {!isCollapsed && (
            <div className="px-3 py-4 border-t border-gray-100 mt-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center">
                  <Store className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">Store Admin</p>
                  <p className="text-xs text-gray-500 truncate">Caparan Tindahan</p>
                </div>
                <Link
                  href="/settings"
                  className="p-1.5 rounded-full hover:bg-gray-200 transition-colors duration-200"
                  title="Settings"
                  aria-label="Go to settings"
                >
                  <Settings className="h-4 w-4 text-gray-600" />
                </Link>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}
