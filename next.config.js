/** @type {import('next').NextConfig} */
const nextConfig = {
  // Complete SSR disable for hydration issues
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // React strict mode disabled
  reactStrictMode: false,

  // Power by header
  poweredByHeader: false,

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Webpack configuration from backup with aggressive caching disabled
  webpack: (config, { dev, isServer }) => {
    // Completely disable caching in development
    if (dev) {
      config.cache = false

      // Clear any existing module cache
      if (require.cache) {
        Object.keys(require.cache).forEach(key => {
          if (key.includes('Navigation') || key.includes('DashboardLayout')) {
            delete require.cache[key]
          }
        })
      }

      // Force fresh module resolution
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/components/NavigationSidebar': require.resolve('./src/components/YoutubeSidebar.tsx'),
        '@/components/Navigation': require.resolve('./src/components/YoutubeSidebar.tsx'),
        '@/components/DashboardLayout': require.resolve('./src/components/ClientOnlyLayout.tsx'),
      }

      // Disable optimizations that might cause caching
      config.optimization = {
        ...config.optimization,
        concatenateModules: false,
        splitChunks: false,
        runtimeChunk: false,
      }

      // Force rebuild on every change
      config.watchOptions = {
        ...config.watchOptions,
        ignored: /node_modules/,
        poll: 500,
        aggregateTimeout: 100,
      }
    }

    // Bundle analyzer (only in development)
    if (process.env.ANALYZE === 'true' && !isServer) {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: '../bundle-analyzer-report.html'
        })
      )
    }

    return config
  },
}

module.exports = nextConfig
