'use client'

import { useState, useEffect } from 'react'

interface NavigationData {
  productsCount: number
  customersCount: number
  debtsCount: number
  paymentsCount: number
  loading: boolean
}

interface DebtData {
  id: string
  isPaid: boolean
  totalAmount: number
  dateOfDebt: string
  customer?: {
    firstName: string
    lastName: string
  }
}

export function useNavigationData(): NavigationData {
  const [data, setData] = useState<NavigationData>({
    productsCount: 0,
    customersCount: 0,
    debtsCount: 0,
    paymentsCount: 0,
    loading: true,
  })

  useEffect(() => {
    const fetchNavigationData = async () => {
      try {
        // Fetch data in parallel for better performance with timeout
        const fetchWithTimeout = (url: string, timeout = 5000) => {
          return Promise.race([
            fetch(url).then(res => res.ok ? res.json() : []),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout')), timeout)
            )
          ])
        }

        const [productsRes, customersRes, debtsRes, paymentsRes] = await Promise.allSettled([
          fetchWithTimeout('/api/products'),
          fetchWithTimeout('/api/customers'),
          fetchWithTimeout('/api/debts'),
          fetchWithTimeout('/api/payments'),
        ])

        const productsCount = productsRes.status === 'fulfilled' && Array.isArray(productsRes.value)
          ? productsRes.value.length : 0
        const customersCount = customersRes.status === 'fulfilled' && Array.isArray(customersRes.value)
          ? customersRes.value.length : 0
        const debtsCount = debtsRes.status === 'fulfilled' && Array.isArray(debtsRes.value)
          ? debtsRes.value.filter((debt: DebtData) => !debt.isPaid).length : 0
        const paymentsCount = paymentsRes.status === 'fulfilled' && Array.isArray(paymentsRes.value)
          ? paymentsRes.value.length : 0

        setData({
          productsCount,
          customersCount,
          debtsCount,
          paymentsCount,
          loading: false,
        })
      } catch (error) {
        console.error('Error fetching navigation data:', error)
        // Set fallback data on error
        setData({
          productsCount: 0,
          customersCount: 0,
          debtsCount: 0,
          paymentsCount: 0,
          loading: false,
        })
      }
    }

    fetchNavigationData()

    // Refresh data every 5 minutes
    const interval = setInterval(fetchNavigationData, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  return data
}
