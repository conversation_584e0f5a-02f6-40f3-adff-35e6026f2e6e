/** @type {import('next').NextConfig} */
const nextConfig = {
  // Experimental features
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // React strict mode
  reactStrictMode: true,

  // Power by header
  poweredByHeader: false,

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Clean webpack configuration
  webpack: (config, { dev, isServer }) => {
    // Only add bundle analyzer when explicitly requested
    if (process.env.ANALYZE === 'true' && !isServer) {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: '../bundle-analyzer-report.html'
        })
      )
    }

    return config
  },
}

module.exports = nextConfig
